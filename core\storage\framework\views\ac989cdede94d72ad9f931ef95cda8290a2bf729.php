<!-- page content start -->
<div class=" pb-3">
    <div id="carouselExampleControls" class="carousel slide" data-ride="carousel">

        <div class="carousel-inner shadow-sm" style="border-radius: 5px">

            <?php $i=0; ?>
            <?php $__empty_1 = true; $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <?php
                    $actives = '';
                ?>

                <?php if($i == 0): ?>
                    <?php $actives = 'active';?>
                <?php endif; ?>
                <?php $i=$i+1; ?>

                <div class="carousel-item <?= $actives ?>">
                    <img class="d-block w-100"
                        src="<?php echo e(getImage('assets/images/frontend/banner/' . $item->data_values->image)); ?>" alt="banner">
                </div>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <?php endif; ?>

        </div>
        <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="sr-only">Previous</span>
        </a>
        <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="sr-only">Next</span>
        </a>
    </div>
</div>



<!-- Scroling Notice -->


<div class="container">
    <div class="row">
        <div class="col-12 mb-3">
            <div class="row">
                    <?php if(auth()->guard()->check()): ?>
                    <div class="col-3 text-center">
                        <a href="<?php echo e(route('income.guide')); ?>" class="card-box">
                            <img src="https://img.icons8.com/3d-fluency/94/signpost.png"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>Earning Guide</p>
                        </a>
                    </div>
                    <div class="col-3 text-center">
                        <a href="<?php echo e(route('plans')); ?>" class="card-box">
                            <img src="https://img.icons8.com/?size=100&id=11824&format=png&color=000000"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>Copy Trade</p>
                        </a>
                    </div>
                    <div class="col-3 text-center">
                        <a href="<?php echo e(route('user.ptc.index')); ?>" class="card-box">
                            <img src="https://img.icons8.com/3d-fluency/60/robot.png"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>Collect Profit</p>
                        </a>
                    </div>
                   <div class="col-3 text-center">
                        <a href="<?php echo e(route('user.coin.mining')); ?>" class="card-box">
                            <img src="https://img.icons8.com/3d-fluency/60/coin.png"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>Coin Mining</p>
                        </a>
                    </div>
                    <div class="col-3 text-center">
                        <a href="javascript:void(0)" data-toggle="modal" data-target="#appDownloadModal" class="card-box">
                            <img src="https://img.icons8.com/3d-fluency/94/download-from-cloud.png"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>Apps</p>
                        </a>
                    </div>
                    <?php endif; ?>

                    <div class="col-3 text-center">
                        <a href="https://crypto.news/" class="card-box">
                            
                       <!-- <a href="<?php echo e(route('blog')); ?>" class="card-box">-->
                            <img src="https://img.icons8.com/3d-fluency/94/news.png"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>News</p>
                        </a>
                    </div>
                    <div class="col-3 text-center">
                        <a href="<?php echo e(@$yourLinks->data_values->video); ?>" class="card-box">
                            <img src="https://img.icons8.com/3d-fluency/94/classroom.png"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>Tutorial</p>
                        </a>
                    </div>
                    <div class="col-3 text-center">
                        <a href="<?php echo e(route('user.community')); ?>" class="card-box">
                            <img src="https://img.icons8.com/3d-fluency/94/conference-call--v2.png"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>Community</p>
                        </a>
                    </div>
                    <div class="col-3 text-center">
                        <a href="<?php echo e(route('pages', 'about-us')); ?>" class="card-box">
                            <img src="https://img.icons8.com/3d-fluency/50/about.png"
                                style="max-width: 60px; max-height: 60px;" alt="">
                            <p>About Us</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

<div class="card mt-2" style="height: 500px">
    <div class="card-body">
        <!-- TradingView Widget BEGIN -->
        <div class="tradingview-widget-container">
            <div class="tradingview-widget-container__widget"></div>
            <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-market-overview.js" async>
                {
                    "colorTheme": "dark",
                    "dateRange": "12M",
                    "showChart": false,
                    "locale": "en",
                    "width": "100%",
                    "height": "100%",
                    "largeChartUrl": "",
                    "isTransparent": true,
                    "showSymbolLogo": true,
                    "showFloatingTooltip": false,
                    "tabs": [
                    {
                        "title": "Crypto",
                        "symbols": [
                        {
                            "s": "BINANCE:BTCUSDT"
                        },
                        {
                            "s": "BINANCE:ETHUSDT"
                        },
                        {
                            "s": "BINANCE:XRPUSDT"
                        },
                        {
                            "s": "BINANCE:DOGEUSDT"
                        },
                        {
                            "s": "BINANCE:SOLUSDT"
                        },
                        {
                            "s": "BINANCE:MATICUSDT"
                        },
                        {
                            "s": "BINANCE:LINKUSDT"
                        },
                        {
                            "s": "BINANCE:COMPUSDT"
                        },
                        {
                            "s": "BINANCE:AVAXUSDT"
                        },
                        {
                            "s": "BINANCE:DOTUSDT"
                        },
                        {
                            "s": "BINANCE:GALAUSDT"
                        }
                        ],
                        "originalTitle": "Indices"
                    }
                    ]
                }
            </script>
        </div>
        <!-- TradingView Widget END -->
    </div>
</div>




<!-- Swiper Our Reviews-->


<?php /**PATH C:\Users\<USER>\Videos\oldaiminning\core\resources\views/templates/basic/content/home_content.blade.php ENDPATH**/ ?>