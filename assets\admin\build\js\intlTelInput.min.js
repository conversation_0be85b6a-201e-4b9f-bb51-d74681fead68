/*
 * International Telephone Input v14.0.0
 * https://github.com/jackocnr/intl-tel-input.git
 * Licensed under the MIT license
 */

!function(a){var b=function(a,b,c){"use strict";return function(){for(var c=[["Afghanistan (‫افغانستان‬‎)","af","93"],["Albania (Shqipëri)","al","355"],["Algeria (‫الجزائر‬‎)","dz","213"],["American Samoa","as","1684"],["Andorra","ad","376"],["Angola","ao","244"],["Anguilla","ai","1264"],["Antigua and Barbuda","ag","1268"],["Argentina","ar","54"],["Armenia (Հայաստան)","am","374"],["Aruba","aw","297"],["Australia","au","61",0],["Austria (Österreich)","at","43"],["Azerbaijan (Azərbaycan)","az","994"],["Bahamas","bs","1242"],["Bahrain (‫البحرين‬‎)","bh","973"],["Bangladesh (বাংলাদেশ)","bd","880"],["Barbados","bb","1246"],["Belarus (Беларусь)","by","375"],["Belgium (België)","be","32"],["Belize","bz","501"],["Benin (Bénin)","bj","229"],["Bermuda","bm","1441"],["Bhutan (འབྲུག)","bt","975"],["Bolivia","bo","591"],["Bosnia and Herzegovina (Босна и Херцеговина)","ba","387"],["Botswana","bw","267"],["Brazil (Brasil)","br","55"],["British Indian Ocean Territory","io","246"],["British Virgin Islands","vg","1284"],["Brunei","bn","673"],["Bulgaria (България)","bg","359"],["Burkina Faso","bf","226"],["Burundi (Uburundi)","bi","257"],["Cambodia (កម្ពុជា)","kh","855"],["Cameroon (Cameroun)","cm","237"],["Canada","ca","1",1,["204","226","236","249","250","289","306","343","365","387","403","416","418","431","437","438","450","506","514","519","548","579","581","587","604","613","639","647","672","705","709","742","778","780","782","807","819","825","867","873","902","905"]],["Cape Verde (Kabu Verdi)","cv","238"],["Caribbean Netherlands","bq","599",1],["Cayman Islands","ky","1345"],["Central African Republic (République centrafricaine)","cf","236"],["Chad (Tchad)","td","235"],["Chile","cl","56"],["China (中国)","cn","86"],["Christmas Island","cx","61",2],["Cocos (Keeling) Islands","cc","61",1],["Colombia","co","57"],["Comoros (‫جزر القمر‬‎)","km","269"],["Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)","cd","243"],["Congo (Republic) (Congo-Brazzaville)","cg","242"],["Cook Islands","ck","682"],["Costa Rica","cr","506"],["Côte d’Ivoire","ci","225"],["Croatia (Hrvatska)","hr","385"],["Cuba","cu","53"],["Curaçao","cw","599",0],["Cyprus (Κύπρος)","cy","357"],["Czech Republic (Česká republika)","cz","420"],["Denmark (Danmark)","dk","45"],["Djibouti","dj","253"],["Dominica","dm","1767"],["Dominican Republic (República Dominicana)","do","1",2,["809","829","849"]],["Ecuador","ec","593"],["Egypt (‫مصر‬‎)","eg","20"],["El Salvador","sv","503"],["Equatorial Guinea (Guinea Ecuatorial)","gq","240"],["Eritrea","er","291"],["Estonia (Eesti)","ee","372"],["Ethiopia","et","251"],["Falkland Islands (Islas Malvinas)","fk","500"],["Faroe Islands (Føroyar)","fo","298"],["Fiji","fj","679"],["Finland (Suomi)","fi","358",0],["France","fr","33"],["French Guiana (Guyane française)","gf","594"],["French Polynesia (Polynésie française)","pf","689"],["Gabon","ga","241"],["Gambia","gm","220"],["Georgia (საქართველო)","ge","995"],["Germany (Deutschland)","de","49"],["Ghana (Gaana)","gh","233"],["Gibraltar","gi","350"],["Greece (Ελλάδα)","gr","30"],["Greenland (Kalaallit Nunaat)","gl","299"],["Grenada","gd","1473"],["Guadeloupe","gp","590",0],["Guam","gu","1671"],["Guatemala","gt","502"],["Guernsey","gg","44",1],["Guinea (Guinée)","gn","224"],["Guinea-Bissau (Guiné Bissau)","gw","245"],["Guyana","gy","592"],["Haiti","ht","509"],["Honduras","hn","504"],["Hong Kong (香港)","hk","852"],["Hungary (Magyarország)","hu","36"],["Iceland (Ísland)","is","354"],["India (भारत)","in","91"],["Indonesia","id","62"],["Iran (‫ایران‬‎)","ir","98"],["Iraq (‫العراق‬‎)","iq","964"],["Ireland","ie","353"],["Isle of Man","im","44",2],["Israel (‫ישראל‬‎)","il","972"],["Italy (Italia)","it","39",0],["Jamaica","jm","1",4,["876","658"]],["Japan (日本)","jp","81"],["Jersey","je","44",3],["Jordan (‫الأردن‬‎)","jo","962"],["Kazakhstan (Казахстан)","kz","7",1],["Kenya","ke","254"],["Kiribati","ki","686"],["Kosovo","xk","383"],["Kuwait (‫الكويت‬‎)","kw","965"],["Kyrgyzstan (Кыргызстан)","kg","996"],["Laos (ລາວ)","la","856"],["Latvia (Latvija)","lv","371"],["Lebanon (‫لبنان‬‎)","lb","961"],["Lesotho","ls","266"],["Liberia","lr","231"],["Libya (‫ليبيا‬‎)","ly","218"],["Liechtenstein","li","423"],["Lithuania (Lietuva)","lt","370"],["Luxembourg","lu","352"],["Macau (澳門)","mo","853"],["Macedonia (FYROM) (Македонија)","mk","389"],["Madagascar (Madagasikara)","mg","261"],["Malawi","mw","265"],["Malaysia","my","60"],["Maldives","mv","960"],["Mali","ml","223"],["Malta","mt","356"],["Marshall Islands","mh","692"],["Martinique","mq","596"],["Mauritania (‫موريتانيا‬‎)","mr","222"],["Mauritius (Moris)","mu","230"],["Mayotte","yt","262",1],["Mexico (México)","mx","52"],["Micronesia","fm","691"],["Moldova (Republica Moldova)","md","373"],["Monaco","mc","377"],["Mongolia (Монгол)","mn","976"],["Montenegro (Crna Gora)","me","382"],["Montserrat","ms","1664"],["Morocco (‫المغرب‬‎)","ma","212",0],["Mozambique (Moçambique)","mz","258"],["Myanmar (Burma) (မြန်မာ)","mm","95"],["Namibia (Namibië)","na","264"],["Nauru","nr","674"],["Nepal (नेपाल)","np","977"],["Netherlands (Nederland)","nl","31"],["New Caledonia (Nouvelle-Calédonie)","nc","687"],["New Zealand","nz","64"],["Nicaragua","ni","505"],["Niger (Nijar)","ne","227"],["Nigeria","ng","234"],["Niue","nu","683"],["Norfolk Island","nf","672"],["North Korea (조선 민주주의 인민 공화국)","kp","850"],["Northern Mariana Islands","mp","1670"],["Norway (Norge)","no","47",0],["Oman (‫عُمان‬‎)","om","968"],["Pakistan (‫پاکستان‬‎)","pk","92"],["Palau","pw","680"],["Palestine (‫فلسطين‬‎)","ps","970"],["Panama (Panamá)","pa","507"],["Papua New Guinea","pg","675"],["Paraguay","py","595"],["Peru (Perú)","pe","51"],["Philippines","ph","63"],["Poland (Polska)","pl","48"],["Portugal","pt","351"],["Puerto Rico","pr","1",3,["787","939"]],["Qatar (‫قطر‬‎)","qa","974"],["Réunion (La Réunion)","re","262",0],["Romania (România)","ro","40"],["Russia (Россия)","ru","7",0],["Rwanda","rw","250"],["Saint Barthélemy","bl","590",1],["Saint Helena","sh","290"],["Saint Kitts and Nevis","kn","1869"],["Saint Lucia","lc","1758"],["Saint Martin (Saint-Martin (partie française))","mf","590",2],["Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)","pm","508"],["Saint Vincent and the Grenadines","vc","1784"],["Samoa","ws","685"],["San Marino","sm","378"],["São Tomé and Príncipe (São Tomé e Príncipe)","st","239"],["Saudi Arabia (‫المملكة العربية السعودية‬‎)","sa","966"],["Senegal (Sénégal)","sn","221"],["Serbia (Србија)","rs","381"],["Seychelles","sc","248"],["Sierra Leone","sl","232"],["Singapore","sg","65"],["Sint Maarten","sx","1721"],["Slovakia (Slovensko)","sk","421"],["Slovenia (Slovenija)","si","386"],["Solomon Islands","sb","677"],["Somalia (Soomaaliya)","so","252"],["South Africa","za","27"],["South Korea (대한민국)","kr","82"],["South Sudan (‫جنوب السودان‬‎)","ss","211"],["Spain (España)","es","34"],["Sri Lanka (ශ්‍රී ලංකාව)","lk","94"],["Sudan (‫السودان‬‎)","sd","249"],["Suriname","sr","597"],["Svalbard and Jan Mayen","sj","47",1],["Swaziland","sz","268"],["Sweden (Sverige)","se","46"],["Switzerland (Schweiz)","ch","41"],["Syria (‫سوريا‬‎)","sy","963"],["Taiwan (台灣)","tw","886"],["Tajikistan","tj","992"],["Tanzania","tz","255"],["Thailand (ไทย)","th","66"],["Timor-Leste","tl","670"],["Togo","tg","228"],["Tokelau","tk","690"],["Tonga","to","676"],["Trinidad and Tobago","tt","1868"],["Tunisia (‫تونس‬‎)","tn","216"],["Turkey (Türkiye)","tr","90"],["Turkmenistan","tm","993"],["Turks and Caicos Islands","tc","1649"],["Tuvalu","tv","688"],["U.S. Virgin Islands","vi","1340"],["Uganda","ug","256"],["Ukraine (Україна)","ua","380"],["United Arab Emirates (‫الإمارات العربية المتحدة‬‎)","ae","971"],["United Kingdom","gb","44",0],["United States","us","1",0],["Uruguay","uy","598"],["Uzbekistan (Oʻzbekiston)","uz","998"],["Vanuatu","vu","678"],["Vatican City (Città del Vaticano)","va","39",1],["Venezuela","ve","58"],["Vietnam (Việt Nam)","vn","84"],["Wallis and Futuna (Wallis-et-Futuna)","wf","681"],["Western Sahara (‫الصحراء الغربية‬‎)","eh","212",1],["Yemen (‫اليمن‬‎)","ye","967"],["Zambia","zm","260"],["Zimbabwe","zw","263"],["Åland Islands","ax","358",1]],d=0;d<c.length;d++){var e=c[d];c[d]={name:e[0],iso2:e[1],dialCode:e[2],priority:e[3]||0,areaCodes:e[4]||null}}a.intlTelInputGlobals={instances:{}};var f=0,g={allowDropdown:!0,autoHideDialCode:!0,autoPlaceholder:"polite",customPlaceholder:null,dropdownContainer:null,excludeCountries:[],formatOnDisplay:!0,geoIpLookup:null,hiddenInput:"",initialCountry:"",localizedCountries:null,nationalMode:!0,onlyCountries:[],placeholderNumberType:"MOBILE",preferredCountries:["us","gb"],separateDialCode:!1,utilsScript:""},h=["800","822","833","844","855","866","877","880","881","882","883","884","885","886","887","888","889"];a.addEventListener("load",function(){a.intlTelInputGlobals.windowLoaded=!0});var i=function(a,b){for(var c=Object.keys(a),d=0;d<c.length;d++)b(c[d],a[c[d]])},j=function(b){i(a.intlTelInputGlobals.instances,function(c,d){a.intlTelInputGlobals.instances[c][b]()})},k=function(a,b){var c=this;this.id=f++,this.a=a;var d=b||{};this.b={},i(g,function(a,b){c.b[a]=d.hasOwnProperty(a)?d[a]:b}),this.e=Boolean(a.getAttribute("placeholder"))};k.prototype={_a:function(){var a=this;if(this.b.nationalMode&&(this.b.autoHideDialCode=!1),this.b.separateDialCode&&(this.b.autoHideDialCode=this.b.nationalMode=!1),this.g=/Android.+Mobile|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),this.g&&(b.body.classList.add("iti-mobile"),this.b.dropdownContainer||(this.b.dropdownContainer=b.body)),"undefined"!=typeof Promise){var c=new Promise(function(b,c){a.h=b,a.rejectAutoCountryPromise=c}),d=new Promise(function(b,c){a.i=b,a.rejectUtilsScriptPromise=c});this.promise=Promise.all([c,d])}else this.h=this.rejectAutoCountryPromise=this.i=this.rejectUtilsScriptPromise=function(){};this.s={},this._b(),this._f(),this._h(),this._i(),this._i2()},_b:function(){this._d(),this._d2(),this._e(),this.b.localizedCountries&&this._translateCountriesByLocale(),(this.b.onlyCountries.length||this.b.localizedCountries)&&this.p.sort(this._countryNameSort)},_c:function(a,b,c){this.q.hasOwnProperty(b)||(this.q[b]=[]);var d=c||0;this.q[b][d]=a},_d:function(){if(this.b.onlyCountries.length){var a=this.b.onlyCountries.map(function(a){return a.toLowerCase()});this.p=c.filter(function(b){return a.indexOf(b.iso2)>-1})}else if(this.b.excludeCountries.length){var b=this.b.excludeCountries.map(function(a){return a.toLowerCase()});this.p=c.filter(function(a){return-1===b.indexOf(a.iso2)})}else this.p=c},_translateCountriesByLocale:function(){for(var a=0;a<this.p.length;a++){var b=this.p[a].iso2.toLowerCase();this.b.localizedCountries.hasOwnProperty(b)&&(this.p[a].name=this.b.localizedCountries[b])}},_countryNameSort:function(a,b){return a.name.localeCompare(b.name)},_d2:function(){this.q={};for(var a=0;a<this.p.length;a++){var b=this.p[a];if(this._c(b.iso2,b.dialCode,b.priority),b.areaCodes)for(var c=0;c<b.areaCodes.length;c++)this._c(b.iso2,b.dialCode+b.areaCodes[c])}},_e:function(){this.preferredCountries=[];for(var a=0;a<this.b.preferredCountries.length;a++){var b=this.b.preferredCountries[a].toLowerCase(),c=this._y(b,!1,!0);c&&this.preferredCountries.push(c)}},_createEl:function(a,c,d){var e=b.createElement(a);return c&&i(c,function(a,b){e.setAttribute(a,b)}),d&&d.appendChild(e),e},_f:function(){this.a.setAttribute("autocomplete","off");var a="intl-tel-input";this.b.allowDropdown&&(a+=" allow-dropdown"),this.b.separateDialCode&&(a+=" separate-dial-code");var b=this._createEl("div",{"class":a});if(this.a.parentNode.insertBefore(b,this.a),this.k=this._createEl("div",{"class":"flag-container"},b),b.appendChild(this.a),this.selectedFlag=this._createEl("div",{"class":"selected-flag",role:"combobox","aria-owns":"country-listbox"},this.k),this.l=this._createEl("div",{"class":"iti-flag"},this.selectedFlag),this.b.separateDialCode&&(this.t=this._createEl("div",{"class":"selected-dial-code"},this.selectedFlag)),this.b.allowDropdown&&(this.selectedFlag.setAttribute("tabindex","0"),this.dropdownArrow=this._createEl("div",{"class":"iti-arrow"},this.selectedFlag),this.m=this._createEl("ul",{"class":"country-list hide",id:"country-listbox","aria-expanded":"false",role:"listbox"}),this.preferredCountries.length&&(this._g(this.preferredCountries,"preferred"),this._createEl("li",{"class":"divider",role:"separator","aria-disabled":"true"},this.m)),this._g(this.p,"standard"),this.b.dropdownContainer?(this.dropdown=this._createEl("div",{"class":"intl-tel-input iti-container"}),this.dropdown.appendChild(this.m)):this.k.appendChild(this.m)),this.b.hiddenInput){var c=this.b.hiddenInput,d=this.a.getAttribute("name");if(d){var e=d.lastIndexOf("[");-1!==e&&(c=d.substr(0,e)+"["+c+"]")}this.hiddenInput=this._createEl("input",{type:"hidden",name:c}),b.appendChild(this.hiddenInput)}},_g:function(a,b){for(var c="",d=0;d<a.length;d++){var e=a[d];c+="<li class='country "+b+"' id='iti-item-"+e.iso2+"' role='option' data-dial-code='"+e.dialCode+"' data-country-code='"+e.iso2+"'>",c+="<div class='flag-box'><div class='iti-flag "+e.iso2+"'></div></div>",c+="<span class='country-name'>"+e.name+"</span>",c+="<span class='dial-code'>+"+e.dialCode+"</span>",c+="</li>"}this.m.insertAdjacentHTML("beforeend",c)},_h:function(){var a=this.a.value,b=this._af(a),c=this._isRegionlessNanp(a);b&&!c?this._v(a):"auto"!==this.b.initialCountry&&(this.b.initialCountry?this._z(this.b.initialCountry.toLowerCase()):b&&c?this._z("us"):(this.j=this.preferredCountries.length?this.preferredCountries[0].iso2:this.p[0].iso2,a||this._z(this.j)),a||this.b.nationalMode||this.b.autoHideDialCode||this.b.separateDialCode||(this.a.value="+"+this.s.dialCode)),a&&this._u(a)},_i:function(){this._j(),this.b.autoHideDialCode&&this._l(),this.b.allowDropdown&&this._i1(),this.hiddenInput&&this._initHiddenInputListener()},_initHiddenInputListener:function(){var a=this;this._handleHiddenInputSubmit=function(){a.hiddenInput.value=a.getNumber()};var b=this.a.form;b&&b.addEventListener("submit",this._handleHiddenInputSubmit)},_getClosestLabel:function(){for(var a=this.a;a&&"LABEL"!==a.tagName;)a=a.parentNode;return a},_i1:function(){var a=this;this._handleLabelClick=function(b){a.m.classList.contains("hide")?a.a.focus():b.preventDefault()};var b=this._getClosestLabel();b&&b.addEventListener("click",this._handleLabelClick),this._handleClickSelectedFlag=function(){!a.m.classList.contains("hide")||a.a.disabled||a.a.readOnly||a._n()},this.selectedFlag.addEventListener("click",this._handleClickSelectedFlag),this._handleFlagsContainerKeydown=function(b){a.m.classList.contains("hide")&&-1!==["ArrowUp","ArrowDown"," ","Enter"].indexOf(b.key)&&(b.preventDefault(),b.stopPropagation(),a._n()),"Tab"===b.key&&a._ac()},this.k.addEventListener("keydown",this._handleFlagsContainerKeydown)},_i2:function(){var b=this;this.b.utilsScript&&!a.intlTelInputUtils?a.intlTelInputGlobals.windowLoaded?a.intlTelInputGlobals.loadUtils(this.b.utilsScript):a.addEventListener("load",function(){a.intlTelInputGlobals.loadUtils(b.b.utilsScript)}):this.i(),"auto"===this.b.initialCountry?this._i3():this.h()},_i3:function(){a.intlTelInputGlobals.autoCountry?this.handleAutoCountry():a.intlTelInputGlobals.startedLoadingAutoCountry||(a.intlTelInputGlobals.startedLoadingAutoCountry=!0,"function"==typeof this.b.geoIpLookup&&this.b.geoIpLookup(function(b){a.intlTelInputGlobals.autoCountry=b.toLowerCase(),setTimeout(function(){j("handleAutoCountry")})},function(){j("rejectAutoCountryPromise")}))},_j:function(){var a=this;this._handleKeyupEvent=function(){a._v(a.a.value)&&a._triggerCountryChange()},this.a.addEventListener("keyup",this._handleKeyupEvent),this._handleClipboardEvent=function(){setTimeout(a._handleKeyupEvent)},this.a.addEventListener("cut",this._handleClipboardEvent),this.a.addEventListener("paste",this._handleClipboardEvent)},_j2:function(a){var b=this.a.getAttribute("maxlength");return b&&a.length>b?a.substr(0,b):a},_l:function(){var a=this;this._handleMousedownFocusEvent=function(c){a.a===b.activeElement||a.a.value||(c.preventDefault(),a.a.focus())},this.a.addEventListener("mousedown",this._handleMousedownFocusEvent),this._handleKeypressPlusEvent=function(b){"+"===b.key&&(a.a.value="")},this._handleFocusEvent=function(b){a.a.value||a.a.readOnly||!a.s.dialCode||(a.a.value="+"+a.s.dialCode,a.a.addEventListener("keypress",a._handleKeypressPlusEvent),setTimeout(function(){var b=a.a.value.length;a.a.setSelectionRange(b,b)}))},this.a.addEventListener("focus",this._handleFocusEvent),this._handleSubmitOrBlurEvent=function(){a._removeEmptyDialCode()};var c=this.a.form;c&&c.addEventListener("submit",this._handleSubmitOrBlurEvent),this.a.addEventListener("blur",this._handleSubmitOrBlurEvent)},_removeEmptyDialCode:function(){if("+"===this.a.value.charAt(0)){var a=this._m(this.a.value);a&&this.s.dialCode!=a||(this.a.value="")}this.a.removeEventListener("keypress",this._handleKeypressPlusEvent)},_m:function(a){return a.replace(/\D/g,"")},_trigger:function(a){var c=b.createEvent("Event");c.initEvent(a,!0,!0),this.a.dispatchEvent(c)},_n:function(){this.m.classList.remove("hide"),this.m.setAttribute("aria-expanded","true"),this._o();var a=this.m.querySelector(".active");a&&(this._x(a),this._ad(a)),this._p(),this.dropdownArrow.classList.add("up"),this._trigger("open:countrydropdown")},_toggleClass:function(a,b,c){c&&!a.classList.contains(b)?a.classList.add(b):!c&&a.classList.contains(b)&&a.classList.remove(b)},_o:function(){var c=this;if(this.b.dropdownContainer&&this.b.dropdownContainer.appendChild(this.dropdown),!this.g){var d=this.a.getBoundingClientRect(),e=a.pageYOffset||b.documentElement.scrollTop,f=d.top+e,g=this.m.offsetHeight,h=f+this.a.offsetHeight+g<e+a.innerHeight,i=f-g>e;if(this._toggleClass(this.m,"dropup",!h&&i),this.b.dropdownContainer){var j=!h&&i?0:this.a.offsetHeight;this.dropdown.style.top=f+j+"px",this.dropdown.style.left=d.left+b.body.scrollLeft+"px",this._handleWindowScroll=function(){c._ac()},a.addEventListener("scroll",this._handleWindowScroll)}}},_getClosestListItem:function(a){for(var b=a;b&&b!==this.m&&!b.classList.contains("country");)b=b.parentNode;return b===this.m?null:b},_p:function(){var a=this;this._handleMouseoverCountryList=function(b){var c=a._getClosestListItem(b.target);c&&a._x(c)},this.m.addEventListener("mouseover",this._handleMouseoverCountryList),this._handleClickCountryList=function(b){var c=a._getClosestListItem(b.target);c&&a._ab(c)},this.m.addEventListener("click",this._handleClickCountryList);var c=!0;this._handleClickOffToClose=function(){c||a._ac(),c=!1},b.documentElement.addEventListener("click",this._handleClickOffToClose);var d="",e=null;this._handleKeydownOnDropdown=function(b){b.preventDefault(),"ArrowUp"===b.key||"ArrowDown"===b.key?a._q(b.key):"Enter"===b.key?a._r():"Escape"===b.key?a._ac():/^[a-zA-ZÀ-ÿ ]$/.test(b.key)&&(e&&clearTimeout(e),d+=b.key.toLowerCase(),a._s(d),e=setTimeout(function(){d=""},1e3))},b.addEventListener("keydown",this._handleKeydownOnDropdown)},_q:function(a){var b=this.m.querySelector(".country.highlight"),c="ArrowUp"===a?b.previousElementSibling:b.nextElementSibling;c&&(c.classList.contains("divider")&&(c="ArrowUp"===a?c.previousElementSibling:c.nextElementSibling),this._x(c),this._ad(c))},_r:function(){var a=this.m.querySelector(".country.highlight");a&&this._ab(a)},_s:function(a){for(var b=0;b<this.p.length;b++)if(this._t(this.p[b].name,a)){var c=this.m.querySelector("#iti-item-"+this.p[b].iso2);this._x(c),this._ad(c,!0);break}},_t:function(a,b){return a.substr(0,b.length).toLowerCase()==b},_u:function(b){if(this.b.formatOnDisplay&&a.intlTelInputUtils&&this.s){var c=this.b.separateDialCode||!this.b.nationalMode&&"+"==b.charAt(0)?intlTelInputUtils.numberFormat.INTERNATIONAL:intlTelInputUtils.numberFormat.NATIONAL;b=intlTelInputUtils.formatNumber(b,this.s.iso2,c)}b=this._ah(b),this.a.value=b},_v:function(a){a&&this.b.nationalMode&&"1"==this.s.dialCode&&"+"!=a.charAt(0)&&("1"!=a.charAt(0)&&(a="1"+a),a="+"+a);var b=this._af(a),c=null,d=this._m(a);if(b){var e=this.q[this._m(b)],f=-1!==e.indexOf(this.s.iso2),g="+1"==b&&d.length>=4;if((!("1"==this.s.dialCode)||!this._isRegionlessNanp(d))&&(!f||g))for(var h=0;h<e.length;h++)if(e[h]){c=e[h];break}}else"+"==a.charAt(0)&&d.length?c="":a&&"+"!=a||(c=this.j);return null!==c&&this._z(c)},_isRegionlessNanp:function(a){var b=this._m(a);if("1"==b.charAt(0)){var c=b.substr(1,3);return-1!==h.indexOf(c)}return!1},_x:function(a){var b=this.m.querySelector(".country.highlight");b&&b.classList.remove("highlight"),a.classList.add("highlight")},_y:function(a,b,d){for(var e=b?c:this.p,f=0;f<e.length;f++)if(e[f].iso2==a)return e[f];if(d)return null;throw new Error("No country data for '"+a+"'")},_z:function(a){var c=this.s.iso2?this.s:{};this.s=a?this._y(a,!1,!1):{},this.s.iso2&&(this.j=this.s.iso2),this.l.setAttribute("class","iti-flag "+a);var d=a?this.s.name+": +"+this.s.dialCode:"Unknown";if(this.selectedFlag.setAttribute("title",d),this.b.separateDialCode){var e=this.s.dialCode?"+"+this.s.dialCode:"",f=this.a.parentNode;c.dialCode&&f.classList.remove("iti-sdc-"+(c.dialCode.length+1)),e&&f.classList.add("iti-sdc-"+e.length);var g=b.createTextNode(e);this.t.appendChild(g)}if(this._aa(),this.b.allowDropdown){var h=this.m.querySelector(".country.active");if(h&&(h.classList.remove("active"),h.setAttribute("aria-selected","false")),a){var i=this.m.querySelector("#iti-item-"+a);i.classList.add("active"),i.setAttribute("aria-selected","true"),this.m.setAttribute("aria-activedescendant",i.getAttribute("id"))}}return c.iso2!==a},_aa:function(){var b="aggressive"===this.b.autoPlaceholder||!this.e&&"polite"===this.b.autoPlaceholder;if(a.intlTelInputUtils&&b){var c=intlTelInputUtils.numberType[this.b.placeholderNumberType],d=this.s.iso2?intlTelInputUtils.getExampleNumber(this.s.iso2,this.b.nationalMode,c):"";d=this._ah(d),"function"==typeof this.b.customPlaceholder&&(d=this.b.customPlaceholder(d,this.s)),this.a.setAttribute("placeholder",d)}},_ab:function(a){var b=this._z(a.getAttribute("data-country-code"));this._ac(),this._ae(a.getAttribute("data-dial-code"),!0),this.a.focus();var c=this.a.value.length;this.a.setSelectionRange(c,c),b&&this._triggerCountryChange()},_ac:function(){this.m.classList.add("hide"),this.m.setAttribute("aria-expanded","false"),this.dropdownArrow.classList.remove("up"),b.removeEventListener("keydown",this._handleKeydownOnDropdown),b.documentElement.removeEventListener("click",this._handleClickOffToClose),this.m.removeEventListener("mouseover",this._handleMouseoverCountryList),this.m.removeEventListener("click",this._handleClickCountryList),this.b.dropdownContainer&&(this.g||a.removeEventListener("scroll",this._handleWindowScroll),this.dropdown.parentNode&&this.dropdown.parentNode.removeChild(this.dropdown)),this._trigger("close:countrydropdown")},_ad:function(c,d){var e=this.m,f=a.pageYOffset||b.documentElement.scrollTop,g=e.offsetHeight,h=e.getBoundingClientRect().top+f,i=h+g,j=c.offsetHeight,k=c.getBoundingClientRect().top+f,l=k+j,m=k-h+e.scrollTop,n=g/2-j/2;if(k<h)d&&(m-=n),e.scrollTop=m;else if(l>i){d&&(m+=n);var o=g-j;e.scrollTop=m-o}},_ae:function(a,b){var c,d=this.a.value;if(a="+"+a,"+"==d.charAt(0)){var e=this._af(d);c=e?d.replace(e,a):a}else{if(this.b.nationalMode||this.b.separateDialCode)return;if(d)c=a+d;else{if(!b&&this.b.autoHideDialCode)return;c=a}}this.a.value=c},_af:function(a){var b="";if("+"==a.charAt(0))for(var c="",d=0;d<a.length;d++){var e=a.charAt(d);if(!isNaN(parseInt(e,10))&&(c+=e,this.q[c]&&(b=a.substr(0,d+1)),4==c.length))break}return b},_ag:function(){var a=this.a.value.trim(),b=this.s.dialCode,c=this._m(a),d="1"==c.charAt(0)?c:"1"+c;return(this.b.separateDialCode?"+"+b:a&&"+"!=a.charAt(0)&&"1"!=a.charAt(0)&&b&&"1"==b.charAt(0)&&4==b.length&&b!=d.substr(0,4)?b.substr(1):"")+a},_ah:function(a){if(this.b.separateDialCode){var b=this._af(a);if(b){null!==this.s.areaCodes&&(b="+"+this.s.dialCode);var c=" "===a[b.length]||"-"===a[b.length]?b.length+1:b.length;a=a.substr(c)}}return this._j2(a)},_triggerCountryChange:function(){this._trigger("countrychange")},handleAutoCountry:function(){"auto"===this.b.initialCountry&&(this.j=a.intlTelInputGlobals.autoCountry,this.a.value||this.setCountry(this.j),this.h())},handleUtils:function(){a.intlTelInputUtils&&(this.a.value&&this._u(this.a.value),this._aa()),this.i()},destroy:function(){var b=this.a.form;if(this.b.allowDropdown){this._ac(),this.selectedFlag.removeEventListener("click",this._handleClickSelectedFlag),this.k.removeEventListener("keydown",this._handleFlagsContainerKeydown);var c=this._getClosestLabel();c&&c.removeEventListener("click",this._handleLabelClick)}this.hiddenInput&&b&&b.removeEventListener("submit",this._handleHiddenInputSubmit),this.b.autoHideDialCode&&(this.a.removeEventListener("mousedown",this._handleMousedownFocusEvent),this.a.removeEventListener("focus",this._handleFocusEvent),b&&b.removeEventListener("submit",this._handleSubmitOrBlurEvent),this.a.removeEventListener("blur",this._handleSubmitOrBlurEvent)),this.a.removeEventListener("keyup",this._handleKeyupEvent),this.a.removeEventListener("cut",this._handleClipboardEvent),this.a.removeEventListener("paste",this._handleClipboardEvent);var d=this.a.parentNode;d.parentNode.insertBefore(this.a,d),d.parentNode.removeChild(d),delete a.intlTelInputGlobals.instances[this.id]},getExtension:function(){return a.intlTelInputUtils?intlTelInputUtils.getExtension(this._ag(),this.s.iso2):""},getNumber:function(b){return a.intlTelInputUtils?intlTelInputUtils.formatNumber(this._ag(),this.s.iso2,b):""},getNumberType:function(){return a.intlTelInputUtils?intlTelInputUtils.getNumberType(this._ag(),this.s.iso2):-99},getSelectedCountryData:function(){return this.s},getValidationError:function(){return a.intlTelInputUtils?intlTelInputUtils.getValidationError(this._ag(),this.s.iso2):-99},isValidNumber:function(){var b=this._ag().trim(),c=this.b.nationalMode?this.s.iso2:"";return a.intlTelInputUtils?intlTelInputUtils.isValidNumber(b,c):null},setCountry:function(a){a=a.toLowerCase(),this.l.classList.contains(a)||(this._z(a),this._ae(this.s.dialCode,!1),this._triggerCountryChange())},setNumber:function(a){var b=this._v(a);this._u(a),b&&this._triggerCountryChange()},setPlaceholderNumberType:function(a){this.b.placeholderNumberType=a,this._aa()}},a.intlTelInputGlobals.getCountryData=function(){return c};var l=function(a,c,d){var e=b.createElement("script");e.onload=function(){j("handleUtils"),c&&c()},e.onerror=function(){j("rejectUtilsScriptPromise"),d&&d()},e.className="iti-load-utils",e.async=!0,e.src=a,b.body.appendChild(e)};return a.intlTelInputGlobals.loadUtils=function(b){if(!a.intlTelInputUtils&&!a.intlTelInputGlobals.startedLoadingUtilsScript){if(a.intlTelInputGlobals.startedLoadingUtilsScript=!0,"undefined"!=typeof Promise)return new Promise(function(a,c){l(b,a,c)});l(b)}return null},a.intlTelInputGlobals.defaults=g,a.intlTelInputGlobals.version="14.0.0",function(b,c){var d=new k(b,c);return d._a(),a.intlTelInputGlobals.instances[d.id]=d,d}}()}(window,document);"object"==typeof module&&module.exports?module.exports=b:window.intlTelInput=b}();