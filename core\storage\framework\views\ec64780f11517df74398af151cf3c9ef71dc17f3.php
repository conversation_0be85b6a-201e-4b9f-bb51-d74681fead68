<!doctype html>
<html lang="<?php echo e(config('app.locale')); ?>" itemscope itemtype="http://schema.org/WebPage">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title> <?php echo e($general->siteName(__($pageTitle))); ?></title>
    <?php echo $__env->make('partials.seo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Custom Css -->

    <!-- manifest meta -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <link rel="manifest" href="<?php echo e(asset($activeTemplateTrue . 'assets/manifest.json')); ?>" />
    <!-- Material icons-->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Google fonts-->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- swiper CSS -->
    <link href="<?php echo e(asset($activeTemplateTrue . 'assets/vendor/swiper/css/swiper.min.css')); ?>" rel="stylesheet">

    <!-- Custom styles for this template -->
    <link href="<?php echo e(asset($activeTemplateTrue . 'assets/css/style.css')); ?>" rel="stylesheet" id="style">

    <!-- Custom style Blade File for this tamplate -->
    <?php echo $__env->make('templates.basic.custom_css.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Custom style 2 Blade File for this tamplate -->
    <?php echo $__env->make('templates.basic.layouts.custom.css', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <style>
        a {
            text-decoration: none !important;
        }

        .bg-gradiant {
            background-image: linear-gradient(to bottom right, #ffffff36, #00000081) !important;
            color: #FFF;
        }

        .bg-gradiant-alt {
            background-image: linear-gradient(to bottom right, #00000081, #ffffff36) !important;
            color: #FFF;
        }

        .bg-purple {
            background: #560087 !important;
            color: #FFF;
        }
        .text-purple {
            color: #560087 !important;
        }

        .bg-purple-light {
            background-color: #fae0ff !important;
        }

        .bg-orange {
            background: #f76000 !important;
            color: #FFF;
        }

        .btn-mini {
            font-size: 0.6rem;
            line-height: 1;
            border-radius: 80.19999999999999rem;
        }

        .btn-mini2 {
            font-size: 0.7rem;
            line-height: 1.7;
            border-radius: 80.19999999999999rem;
        }

        .border-custom {
            border-radius: 1.3rem !important;
        }

        .avatar.avatar-200 {
            height: 200px;
            line-height: 200px;
            width: 200px;
        }

        /* custom */

        .single-select.active {
            position: relative;
            border-color: #e6a25d;
        }
        .single-select {
            padding: 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid transparent;
            border-radius: 8px;
        }

    </style>

    <link rel="stylesheet" href="<?php echo e(asset('assets/global/css/line-awesome.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue . 'css/lightcase.css')); ?>">

    <?php echo $__env->yieldPushContent('style-lib'); ?>

    <?php echo $__env->yieldPushContent('style'); ?>

    <?php echo $__env->yieldPushContent('style-custom'); ?>

    <link rel="stylesheet" href="<?php echo e(asset($activeTemplateTrue . 'css/color.php')); ?>?color1=<?php echo e($general->base_color); ?>&color2=<?php echo e($general->secondary_color); ?>">


    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />

    <style>
        .swiper-slide {
            background-position: center;
            background-size: cover;
            width: 300px;
            height: auto;
        }
        .custom_preload{
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 9999999999;
        }
        .loderCustom{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50% -50%)
        }

    </style>
</head>

<body>

    <?php echo $__env->yieldPushContent('fbComment'); ?>

    

    <!-- screen loader -->
    


    <!-- scroll-to-top start -->
    
    <!-- scroll-to-top end -->

    <?php echo $__env->yieldContent('panel'); ?>

    


    
    


    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="<?php echo e(asset('assets/global/js/jquery-3.6.0.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/global/js/bootstrap.bundle.min.js')); ?>"></script>


    <!--**** custom script start ****-->
    <!-- Required jquery and libraries -->
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/js/jquery-3.3.1.min.js')); ?>"></script>
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/js/popper.min.js')); ?>"></script>
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/vendor/bootstrap/js/bootstrap.min.js')); ?>"></script>

    <!-- cookie js -->
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/js/jquery.cookie.js')); ?>"></script>

    <!-- Swiper slider  js-->
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/vendor/swiper/js/swiper.min.js')); ?>"></script>

    <!-- Swiper slider  js-->
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/vendor/swiper/js/swiper.min.js')); ?>"></script>

    <!-- chart js-->
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/vendor/chartjs/Chart.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/vendor/chartjs/utils.js')); ?>"></script>
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/vendor/chartjs/chart-js-data.js')); ?>"></script>

    <!-- Customized jquery file  -->
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/js/main.js')); ?>"></script>
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/js/color-scheme-demo.js')); ?>"></script>

    <!-- PWA app service registration and works -->
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/js/pwa-services.js')); ?>"></script>

    <!-- page level custom script -->
    <script src="<?php echo e(asset($activeTemplateTrue . 'assets/js/app.js')); ?>"></script>
    <!--***** custom script end *****-->

    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-element-bundle.min.js"></script>

    <?php echo $__env->yieldPushContent('script-lib'); ?>

    <?php echo $__env->yieldPushContent('script'); ?>

    <?php echo $__env->make('partials.plugins', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->make('partials.notify', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>

    <script>
        //-- Notify --//
        const notifyMsg = (msg,cls) => {
            Swal.fire({
                position: 'center',
                icon: cls,
                title: msg,
                toast:true,
                showConfirmButton: false,
                timer: 2500
            })
        }

        const BtnSPIN = '<div class="spinner-border spinner-border-sm" role="status"></div>';

        //preloader custom//
        // $(window).on('load', function () {
        //     $('#preLoadCustom').delay(100).fadeOut(100);
        // });

        // $('a').click(function () {
        //     $('#preLoadCustom').delay(100).fadeIn(100);
        //     $('#preLoadCustom').delay(100).fadeOut(100);
        // });

        // $('button').click(function () {
        //     $('#preLoadCustom').delay(100).fadeIn(100);
        //     $('#preLoadCustom').delay(100).fadeOut(100);
        // });

        const darkModeChange = () => {
            if($('body').hasClass('darkmode')){
                $('.nightModeImg').html(`<img width="28px" src="<?php echo e(asset('assets/images/new-logo-3d/sun.png')); ?>" alt="">`);
            }else{
                $('.nightModeImg').html(`<img width="28px" src="<?php echo e(asset('assets/images/new-logo-3d/cloud.png')); ?>" alt="">`);
            }
        }

        $(window).on('load', function () {
            darkModeChange()
        });

        $(document).on('click', '#darklayout', function () {
            darkModeChange()
        });

        (function($) {
            "use strict";
            $(".langSel").on("change", function() {
                window.location.href = "<?php echo e(route('home')); ?>/change/" + $(this).val();
            });

            var inputElements = $('input,select');
            $.each(inputElements, function(index, element) {
                element = $(element);
                var type = element.attr('type');
                if (type != 'checkbox') {
                    element.closest('.form-group').find('label').attr('for', element.attr('name'));
                    element.attr('id', element.attr('name'))
                }
            });

            $('.policy').on('click', function() {
                $.get('<?php echo e(route('cookie.accept')); ?>', function(response) {
                    $('.cookies-card').addClass('d-none');
                });
            });

            setTimeout(function() {
                $('.cookies-card').removeClass('hide')
            }, 2000);

            $.each($('input, select, textarea'), function(i, element) {

                if (element.hasAttribute('required')) {
                    $(element).closest('.form-group').find('label').addClass('required');
                }

            });

            $('.showFilterBtn').on('click', function() {
                $('.responsive-filter-card').slideToggle();
            });

            let headings = $('.table th');
            let rows = $('.table tbody tr');
            let columns
            let dataLabel;
            $.each(rows, function(index, element) {
                columns = element.children;
                if (columns.length == headings.length) {
                    $.each(columns, function(i, td) {
                        dataLabel = headings[i].innerText;
                        $(td).attr('data-label', dataLabel)
                    });
                }
            });

        })(jQuery);
    </script>

<script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>

<script>
    var swiper = new Swiper(".mySwiper", {
    //   effect: "coverflow",
      grabCursor: true,
      centeredSlides: true,
      slidesPerView: "auto",
      coverflowEffect: {
        rotate: 50,
        stretch: 0,
        depth: 100,
        modifier: 1,
        slideShadows: true,
      },
      pagination: {
        el: ".swiper-pagination",
      },
    });
</script>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Videos\oldaiminning\core\resources\views/templates/basic/layouts/app.blade.php ENDPATH**/ ?>