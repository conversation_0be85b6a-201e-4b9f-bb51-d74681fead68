    <?php if(auth()->guard()->check()): ?>
        <?php
            header('Location: user/dashboard');
            die();
        ?>
    <?php endif; ?>


    
    <?php $__env->startSection('content'); ?>
        <?php echo $__env->make('templates.basic.liveonline', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php
            $banners = getContent('banner.element');
            $yourLinks = getContent('links.content', true);
            $fake_reviews = getContent('fake_review.element');
            $noticeCaption = getContent('notice.content', true);
        ?>

        <!-- App download Modal -->
        <?php echo $__env->make('templates.basic.includes.app_down_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <body class="body-scroll d-flex flex-column h-100 menu-overlay" data-page="homepage">

            <style>
                a {
                    text-decoration: none !important;
                    text-decoration-color: none !important;
                }
            </style>

            <!-- Begin page content -->
            <main class="flex-shrink-0 main has-footer">
                <!-- Fixed navbar -->
                <?php echo $__env->make($activeTemplate . 'includes.home.top_nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                

                    <!-- Home Content -->
                    <?php echo $__env->make('templates.basic.content.home_content', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <!-- End Home Content -->

                
            </main>

            <!-- footer-->
            <?php echo $__env->make($activeTemplate . 'includes.home.bottom_nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        </body>

        <?php if($sections->secs != null): ?>
            <?php $__currentLoopData = json_decode($sections->secs); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make($activeTemplate . 'sections.' . $sec, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    <?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.frontend', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Videos\oldaiminning\core\resources\views/templates/basic/home.blade.php ENDPATH**/ ?>