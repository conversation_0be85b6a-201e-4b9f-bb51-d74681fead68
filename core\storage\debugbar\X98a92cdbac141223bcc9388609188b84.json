{"__meta": {"id": "X98a92cdbac141223bcc9388609188b84", "datetime": "2025-07-26 11:22:39", "utime": 1753496559.787254, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[11:22:29] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in C:\\Users\\<USER>\\Videos\\oldaiminning\\core\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1753496549.249657, "collector": "log"}, {"message": "[11:22:29] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in C:\\Users\\<USER>\\Videos\\oldaiminning\\core\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1753496549.249788, "collector": "log"}, {"message": "[11:22:29] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in C:\\Users\\<USER>\\Videos\\oldaiminning\\core\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1753496549.541335, "collector": "log"}, {"message": "[11:22:29] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in C:\\Users\\<USER>\\Videos\\oldaiminning\\core\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1753496549.541479, "collector": "log"}]}, "time": {"start": 1753496544.016478, "end": 1753496559.787286, "duration": 15.770807981491089, "duration_str": "15.77s", "measures": [{"label": "Booting", "start": 1753496544.016478, "relative_start": 0, "end": 1753496547.099162, "relative_end": 1753496547.099162, "duration": 3.08268404006958, "duration_str": "3.08s", "params": [], "collector": null}, {"label": "Application", "start": 1753496547.515988, "relative_start": 3.4995100498199463, "end": 1753496559.787289, "relative_end": 2.86102294921875e-06, "duration": 12.271300792694092, "duration_str": "12.27s", "params": [], "collector": null}]}, "memory": {"peak_usage": 24642200, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 15, "templates": [{"name": "templates.basic.home (\\resources\\views\\templates\\basic\\home.blade.php)", "param_count": 2, "params": ["pageTitle", "sections"], "type": "blade"}, {"name": "templates.basic.liveonline (\\resources\\views\\templates\\basic\\liveonline.blade.php)", "param_count": 13, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections"], "type": "blade"}, {"name": "templates.basic.includes.app_down_modal (\\resources\\views\\templates\\basic\\includes\\app_down_modal.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "templates.basic.includes.home.top_nav (\\resources\\views\\templates\\basic\\includes\\home\\top_nav.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "templates.basic.includes.color_change (\\resources\\views\\templates\\basic\\includes\\color_change.blade.php)", "param_count": 18, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption", "user"], "type": "blade"}, {"name": "templates.basic.includes.notice_modal (\\resources\\views\\templates\\basic\\includes\\notice_modal.blade.php)", "param_count": 18, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption", "user"], "type": "blade"}, {"name": "templates.basic.content.home_content (\\resources\\views\\templates\\basic\\content\\home_content.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "templates.basic.includes.home.bottom_nav (\\resources\\views\\templates\\basic\\includes\\home\\bottom_nav.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "templates.basic.layouts.frontend (\\resources\\views\\templates\\basic\\layouts\\frontend.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "templates.basic.layouts.app (\\resources\\views\\templates\\basic\\layouts\\app.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "partials.seo (\\resources\\views\\partials\\seo.blade.php)", "param_count": 18, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption", "seo"], "type": "blade"}, {"name": "templates.basic.custom_css.style (\\resources\\views\\templates\\basic\\custom_css\\style.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "templates.basic.layouts.custom.css (\\resources\\views\\templates\\basic\\layouts\\custom\\css.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "partials.plugins (\\resources\\views\\partials\\plugins.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}, {"name": "partials.notify (\\resources\\views\\partials\\notify.blade.php)", "param_count": 17, "params": ["__env", "app", "general", "activeTemplate", "activeTemplateTrue", "language", "emptyMessage", "AllUsers", "withdrawMethod", "gatewayCurrency", "errors", "pageTitle", "sections", "banners", "yourLinks", "fake_reviews", "noticeCaption"], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "checkProject, web, maintenance, web2", "controller": "App\\Http\\Controllers\\SiteController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "home", "file": "<a href=\"phpstorm://open?file=C:\\Users\\<USER>\\Videos\\oldaiminning\\core\\app\\Http\\Controllers\\SiteController.php&line=19\">\\app\\Http\\Controllers\\SiteController.php:19-27</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.18595, "accumulated_duration_str": "186ms", "statements": [{"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Middleware\\LanguageMiddleware.php", "line": 29}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Middleware\\LanguageMiddleware.php", "line": 19}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "demo", "line": 24}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "duration": 0.0195, "duration_str": "19.5ms", "stmt_id": "\\app\\Http\\Middleware\\LanguageMiddleware.php:29", "connection": "aitrading", "start_percent": 0, "width_percent": 10.487}, {"sql": "select * from `pages` where `tempname` = 'templates.basic.' and `slug` = '/' limit 1", "type": "query", "params": [], "bindings": ["templates.basic.", "/"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SiteController.php", "line": 25}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.02966, "duration_str": "29.66ms", "stmt_id": "\\app\\Http\\Controllers\\SiteController.php:25", "connection": "aitrading", "start_percent": 10.487, "width_percent": 15.951}, {"sql": "select * from `frontends` where `data_keys` = 'banner.element' order by `id` desc", "type": "query", "params": [], "bindings": ["banner.element"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 456}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.038450000000000005, "duration_str": "38.45ms", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:456", "connection": "aitrading", "start_percent": 26.437, "width_percent": 20.678}, {"sql": "select * from `frontends` where `data_keys` = 'links.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["links.content"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 425}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:425", "connection": "aitrading", "start_percent": 47.115, "width_percent": 0.323}, {"sql": "select * from `frontends` where `data_keys` = 'fake_review.element' order by `id` desc", "type": "query", "params": [], "bindings": ["fake_review.element"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 456}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.02588, "duration_str": "25.88ms", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:456", "connection": "aitrading", "start_percent": 47.437, "width_percent": 13.918}, {"sql": "select * from `frontends` where `data_keys` = 'notice.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["notice.content"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 425}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.00809, "duration_str": "8.09ms", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:425", "connection": "aitrading", "start_percent": 61.355, "width_percent": 4.351}, {"sql": "select * from `frontends` where `data_keys` = 'links.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["links.content"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 425}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.00391, "duration_str": "3.91ms", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:425", "connection": "aitrading", "start_percent": 65.706, "width_percent": 2.103}, {"sql": "select * from `frontends` where `data_keys` = 'notice.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["notice.content"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 425}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.01776, "duration_str": "17.76ms", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:425", "connection": "aitrading", "start_percent": 67.809, "width_percent": 9.551}, {"sql": "select * from `frontends` where `data_keys` = 'notice.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["notice.content"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 425}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.013550000000000001, "duration_str": "13.55ms", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:425", "connection": "aitrading", "start_percent": 77.36, "width_percent": 7.287}, {"sql": "select * from `frontends` where `data_keys` = 'links.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["links.content"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 425}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:425", "connection": "aitrading", "start_percent": 84.646, "width_percent": 0.43}, {"sql": "select * from `frontends` where `data_keys` = 'seo.data' limit 1", "type": "query", "params": [], "bindings": ["seo.data"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Providers\\AppServiceProvider.php", "line": 74}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 133}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 104}, {"index": 21, "namespace": "view", "name": "ec64780f11517df74398af151cf3c9ef71dc17f3", "line": 10}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Providers\\AppServiceProvider.php:74", "connection": "aitrading", "start_percent": 85.077, "width_percent": 0.43}, {"sql": "select * from `frontends` where `data_keys` = 'links.content' order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["links.content"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 425}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:425", "connection": "aitrading", "start_percent": 85.507, "width_percent": 0.506}, {"sql": "select * from `extensions` where `act` = 'tawk-chat' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["tawk-chat", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 87}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.020370000000000003, "duration_str": "20.37ms", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:87", "connection": "aitrading", "start_percent": 86.012, "width_percent": 10.955}, {"sql": "select * from `extensions` where `act` = 'google-analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["google-analytics", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\helpers.php", "line": 87}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 152}], "duration": 0.00564, "duration_str": "5.64ms", "stmt_id": "\\app\\Http\\Helpers\\helpers.php:87", "connection": "aitrading", "start_percent": 96.967, "width_percent": 3.033}]}, "models": {"data": {"App\\Models\\Frontend": 14, "App\\Models\\Page": 1, "App\\Models\\Language": 1}, "count": 16}, "gate": {"count": 0, "messages": []}, "session": {"_token": "usuiA6YWnaFyXFou2WdL7lfguoKRHrEp4Ni9JuKF", "lang": "en", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-170718893 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-170718893\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1067362637 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1067362637\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-684998123 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-684998123\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-20050381 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,hi;q=0.8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20050381\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1401468110 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\Users\\<USER>\\Videos\\oldaiminning\\core\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57844</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"56 characters\">C:\\Users\\<USER>\\Videos\\oldaiminning\\core\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,hi;q=0.8</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753496544.0165</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753496544</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1401468110\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1529695614 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1529695614\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1042736869 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 02:22:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlpsVm04T1UrYXpkeHY2VlVDd1NqNGc9PSIsInZhbHVlIjoibVlCWmlqUW1ySWZOVERtbHRoK01Cb0thS0RaNjQvSXJhWVpkK3EydG9zYkRJRFUzdEZtZ2dldVNFNm0zb2I1YXdJUGFpNERmTTkwRE9YVml2V3ZBQ2J0UkJwZ0NjRlYzcVZPYVM3SnRZTHhRREozbUd4bUtRK1ZwNHNSMVA1VVMiLCJtYWMiOiJiNDU5YTQzOTVlOGZjMDY1OWQ4NmMxZmZiM2QwNjdmMTZkZDRjMzRlYTdjN2QzNDUzY2RlZDc5ZjQ5YTQ2NDhjIiwidGFnIjoiIn0%3D; expires=Sat, 26 Jul 2025 04:22:39 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImRHOSt0akJob0REdkhuTnhlK2xOK1E9PSIsInZhbHVlIjoiSWNicmJtMnZucUdia2h1TWdOR1V5NjdjV3JyWS8xYitpc2JQc3cxRkN1M1dRN1NReEdScUIrTkJzdjc5UFVzcWlYZmtNc1dIa3VpRkF0SHZlSnhkK3NaaUxJeGVjbGNDNlRIR3hpMm1rLzZhdk85a3Jld256S2pLUnk0Nzd1SEgiLCJtYWMiOiI5MTcxNTczNmRiYWEzYmQ2NzE5ODk5ODRjNWIxOWI0M2U0MDI5NzE0NDMxODAxMWI0YWE0MjBkODkwOTNkOWZkIiwidGFnIjoiIn0%3D; expires=Sat, 26 Jul 2025 04:22:39 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlpsVm04T1UrYXpkeHY2VlVDd1NqNGc9PSIsInZhbHVlIjoibVlCWmlqUW1ySWZOVERtbHRoK01Cb0thS0RaNjQvSXJhWVpkK3EydG9zYkRJRFUzdEZtZ2dldVNFNm0zb2I1YXdJUGFpNERmTTkwRE9YVml2V3ZBQ2J0UkJwZ0NjRlYzcVZPYVM3SnRZTHhRREozbUd4bUtRK1ZwNHNSMVA1VVMiLCJtYWMiOiJiNDU5YTQzOTVlOGZjMDY1OWQ4NmMxZmZiM2QwNjdmMTZkZDRjMzRlYTdjN2QzNDUzY2RlZDc5ZjQ5YTQ2NDhjIiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:22:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImRHOSt0akJob0REdkhuTnhlK2xOK1E9PSIsInZhbHVlIjoiSWNicmJtMnZucUdia2h1TWdOR1V5NjdjV3JyWS8xYitpc2JQc3cxRkN1M1dRN1NReEdScUIrTkJzdjc5UFVzcWlYZmtNc1dIa3VpRkF0SHZlSnhkK3NaaUxJeGVjbGNDNlRIR3hpMm1rLzZhdk85a3Jld256S2pLUnk0Nzd1SEgiLCJtYWMiOiI5MTcxNTczNmRiYWEzYmQ2NzE5ODk5ODRjNWIxOWI0M2U0MDI5NzE0NDMxODAxMWI0YWE0MjBkODkwOTNkOWZkIiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:22:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1042736869\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-663087971 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">usuiA6YWnaFyXFou2WdL7lfguoKRHrEp4Ni9JuKF</span>\"\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663087971\", {\"maxDepth\":0})</script>\n"}}