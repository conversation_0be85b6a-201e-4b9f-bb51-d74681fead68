<?php
$user = Auth::user();
$noticeCaption = getContent('notice.content',true);
?>

<!-- Fixed navbar -->
<header  class="header">
    <div class="row">

        <div class="text-left col align-self-center">
            <a class="navbar-brand" href="<?php echo e(route('home')); ?>">
                <h5 class="mb-0"><img style="height:35px;" src="<?php echo e(getImage(getFilePath('logoIcon') . '/logo.png')); ?>" alt="site-logo"></h5>
            </a>
        </div>
        <div class="ml-auto col-auto pl-0">
            <div class="row">

                <a href="notification.html" class=" btn btn-40 btn-link" data-toggle="modal" data-target="#noticeModal">
                    <img width="25px" style="margin-top: -8px;" src="<?php echo e(asset('assets/images/3d-logo/notice.png')); ?>" alt="">
                </a>
            </div>
        </div>
    </div>

</header>


<?php echo $__env->make(activeTemplate() . 'includes.color_change', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make(activeTemplate() . 'includes.notice_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<?php /**PATH C:\Users\<USER>\Videos\oldaiminning\core\resources\views/templates/basic/includes/home/<USER>/ ?>